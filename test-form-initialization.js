// Test file để kiểm tra việc khởi tạo form fields
// Mô phỏng dữ liệu form fields
const mockFormFields = [
    {
        name: 'text_field',
        type: 'text',
        value: null // Không có giá trị mặc định
    },
    {
        name: 'number_field', 
        type: 'number',
        value: undefined // Không có giá trị mặc định
    },
    {
        name: 'select_field',
        type: 'select',
        // Không có value property
    },
    {
        name: 'checkbox_field',
        type: 'checkbox',
        value: true // Có giá trị mặc định
    },
    {
        name: 'file_field',
        type: 'file'
        // Không có value property
    }
];

// Mô phỏng hàm initializeValues cũ
const oldInitializeValues = (formFields, formData) => {
    formFields.forEach((field) => {
        if (field.value) {
            formData[field.name] = field.value;
        }
    });
};

// Mô phỏng hàm initializeValues mới
const newInitializeValues = (formFields, formData) => {
    formFields.forEach((field) => {
        // Khởi tạo tất cả các trường, kể cả những trường không có giá trị mặc định
        if (field.value !== undefined && field.value !== null) {
            formData[field.name] = field.value;
        } else {
            // Khởi tạo giá trị mặc định dựa trên type của field
            switch (field.type) {
                case 'text':
                case 'textarea':
                case 'email':
                case 'password':
                case 'url':
                    formData[field.name] = '';
                    break;
                case 'number':
                    formData[field.name] = 0;
                    break;
                case 'checkbox':
                    formData[field.name] = false;
                    break;
                case 'select':
                case 'radio':
                    formData[field.name] = '';
                    break;
                case 'checklist':
                    formData[field.name] = [];
                    break;
                case 'date':
                case 'time':
                case 'datetime':
                    formData[field.name] = '';
                    break;
                case 'file':
                    formData[field.name] = [];
                    break;
                default:
                    formData[field.name] = '';
                    break;
            }
        }
    });
};

// Test với hàm cũ
console.log('=== TEST VỚI HÀM CŨ ===');
const oldFormData = {};
oldInitializeValues(mockFormFields, oldFormData);
console.log('Dữ liệu form với hàm cũ:', oldFormData);
console.log('Số lượng trường được khởi tạo:', Object.keys(oldFormData).length);

// Test với hàm mới
console.log('\n=== TEST VỚI HÀM MỚI ===');
const newFormData = {};
newInitializeValues(mockFormFields, newFormData);
console.log('Dữ liệu form với hàm mới:', newFormData);
console.log('Số lượng trường được khởi tạo:', Object.keys(newFormData).length);

// So sánh kết quả
console.log('\n=== SO SÁNH KẾT QUẢ ===');
console.log('Hàm cũ chỉ khởi tạo:', Object.keys(oldFormData));
console.log('Hàm mới khởi tạo tất cả:', Object.keys(newFormData));
